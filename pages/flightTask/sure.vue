<template>
  <view class="receive-page page-bg">
    <!-- 自定义导航栏 -->
    <CustomerNav title="待确认任务" custom-go-back @onBack="onBack" />

    <!-- 任务列表 -->
    <view class="task-list" v-if="taskList.length > 0">
      <view
        class="task-card"
        v-for="(task, index) in taskList"
        :key="index"
        @click="handleTaskClick(task)"
      >
        <view class="task-header">
          <view class="title-box"> {{ task.taskType }}</view>
          <view class="option-box">
            <text>去确认</text>
            <van-icon name="arrow" />
          </view>
        </view>
        <view class="task-content">
          <view class="info-row">
            <view class="info-item">
              <text class="label">注册号：</text>
              <text class="value">{{ task.registrationNumber }}</text>
            </view>
            <view class="info-item">
              <text class="label">机型：</text>
              <text class="value">{{ task.aircraftType }}</text>
            </view>
          </view>

          <view class="info-row" v-if="task.departure">
            <view class="info-item">
              <text class="label">起飞基地：</text>
              <text class="value">{{ task.departure }}</text>
            </view>
          </view>
          <view class="info-row" v-if="task.routeOrAirspaceName">
            <view class="info-item full-width">
              <text class="label">航线/空域：</text>
              <text class="value">{{ task.routeOrAirspaceName }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item full-width">
              <text class="label">飞行日期：</text>
              <text class="value"
                >{{ task.flightDate || '' }}({{
                  getRelativeDateText(task.flightDate)
                }})
              </text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="label">预估架次：</text>
              <text class="value">{{ task.flightFrequency }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item full-width">
              <text class="label">发布时间：</text>
              <text class="value">{{ task.createTime }}</text>
            </view>
          </view>
          <view class="split-line"></view>
          <view class="info-row">
            <view class="info-item full-width">
              <text class="label">已确认部门：</text>
              <view class="value process-box">
                <view
                  v-for="item in processList"
                  :key="item.name"
                  class="process-item"
                  :class="task[item.statusName] === 1 ? 'active' : ''"
                >
                  <view class="circle"></view>
                  <text class="name">{{ item.name }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <Empty v-else />
    <Background />
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import { queryFlightTaskConfig } from '../../api/flightTask'
import { getRelativeDateText } from '../../utils'
import Empty from '../../components/Empty/index.vue'
import Background from '../../components/Background/index.vue'

export default {
  name: 'sure',
  components: { Background, Empty, CustomerNav },
  data() {
    return {
      taskStatus: 1, //1:未确认, 2:未接收
      taskList: [],
      processList: [
        { name: '市场', statusName: 'marketConfirm' },
        { name: '飞行', statusName: 'flightConfirm' },
        { name: '机务', statusName: 'maintenanceConfirm' },
        { name: '运控', statusName: 'ocConfirm' },
      ],
    }
  },
  onShow() {
    this.getData()
  },
  onPullDownRefresh() {
    this.getData().finally(() => {
      uni.stopPullDownRefresh()
    })
  },
  methods: {
    getRelativeDateText,
    onBack() {
      uni.switchTab({
        url: '/pages/home/<USER>',
      })
    },
    // 加载任务列表
    async getData() {
      const res = await queryFlightTaskConfig({
        taskStatus: Number(this.taskStatus),
      })
      if (res.response.code === 200) {
        this.taskList = res.response.data || []
      }
    },
    handleTaskClick(item) {
      uni.navigateTo({
        url: `/pages/flightTask/receiveDetail?id=${item.id}`,
      })
      // if (this.taskStatus === '1') {
      //   uni.navigateTo({
      //     url: `/pages/flightTask/sureDetail?id=${item.id}`,
      //   })
      // } else {
      //   uni.navigateTo({
      //     url: `/pages/flightTask/receiveDetail?id=${item.id}`,
      //   })
      // }
    },
  },
}
</script>

<style scoped lang="scss">
@import '../../assets/css/common.less';

.receive-page {
  min-height: 100vh;
}

.task-list {
  padding: 16px;
}

.task-card {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.task-info {
  padding: 16px;
}

.task-header {
  border-radius: 8px 8px 0 0;
  background: #2c5de5;
  color: #fff;
  box-sizing: border-box;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-box {
    font-weight: bold;
  }

  .option-box {
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    opacity: 0.9;
  }
}

.task-content {
  padding: 16px;
  box-sizing: border-box;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

//.info-row:last-of-type {
//  margin-bottom: 16px;
//}

.info-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 50%;
}

.info-item.full-width {
  flex: 1;
  min-width: 100%;
}

.label {
  color: #666666;
  font-size: 14px;
  margin-right: 4px;
  white-space: nowrap;
}

.value {
  color: #333333;
  font-size: 14px;
  font-weight: 500;
  //white-space: wrap;
  word-break: break-all; /* 强制在单词内换行 */
  white-space: pre-line;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; //行数
  -webkit-box-orient: vertical;
}

//.publish-time {
//  text-align: center;
//  border-top: 1px solid #f0f0f0;
//  margin-top: 8px;
//}

.split-line {
  width: 100%;
  height: 1px;
  margin: 12px 0;
  background: #f0f0f0;
}

.publish-time text {
  color: #666666;
  font-size: 12px;
}

.process-box {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 0;

  .process-item {
    text-align: center;

    .circle {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #bec0c5;
      margin: 0 auto 2px auto;
    }

    .name {
      font-size: 14px;
      color: #bec0c5;
      font-weight: normal;
    }

    &.active {
      .circle {
        //border: 2px solid #008556;
        background: #07c160;
      }

      .name {
        color: #07c160;
      }
    }

    &:last-child {
      .connect-line {
        display: none;
      }
    }
  }
}
</style>
