<template>
  <view class="receive-detail-page">
    <!-- 自定义导航栏 -->
    <CustomerNav title="任务接收" />

    <!-- 内容主体 -->
    <view class="content">
      <!-- 表单区域 -->
      <view class="form-box">
        <van-row>
          <van-col span="12">
            <FormItem label="注册号：" class="text-row" label-width="60px">
              <text>{{ formData.registrationNumber }}</text>
            </FormItem>
          </van-col>
          <van-col span="12">
            <FormItem label="机型：" class="text-row" label-width="50px">
              <text>{{ formData.aircraftType }}</text>
            </FormItem>
          </van-col>
        </van-row>

        <FormItem label="任务性质：" class="text-row">
          <text>{{ formData.taskType }}</text>
        </FormItem>

        <FormItem label="计划时间：" class="text-row">
          <text
            >{{ formData.flightDate || '' }}({{
              getRelativeDateText(formData.flightDate)
            }})
          </text>
        </FormItem>

        <FormItem
          label="起飞基地："
          class="text-row"
          v-if="!!formData.departure"
        >
          <text>{{ formData.departure }}</text>
        </FormItem>
        <FormItem label="航线/空域：" class="text-row">
          <text>{{ formData.routeOrAirspaceName }}</text>
        </FormItem>
        <FormItem label="预估架次：" class="text-row">
          <text>{{ formData.flightFrequency }}</text>
        </FormItem>
        <view class="plan-time-content">
          <view
            v-for="(item, index) in formData.takeOffAndLanding"
            :key="index"
            class="plan-time-item"
          >
            <van-row>
              <van-col span="12">
                <FormItem
                  label="计划起飞："
                  class="text-row time-row"
                  label-width="70px"
                >
                  <text>{{ item.planDepartTime }}</text>
                </FormItem>
              </van-col>
              <van-col span="10">
                <FormItem
                  label="计划到达："
                  class="text-row time-row"
                  label-width="70px"
                >
                  <text>{{ item.planArriveTime }}</text>
                </FormItem>
              </van-col>
            </van-row>
            <FormItem
              label="备注："
              class="text-row time-row"
              label-width="70px"
            >
              <text>{{ item.remark }}</text>
            </FormItem>
          </view>
        </view>
        <view>
          <view class="divider"></view>
          <view v-if="roleType.indexOf('1') > -1">
            <view class="section-title">飞行部人员修改</view>
            <van-row>
              <van-col span="14">
                <FormItem label="机长：" label-width="70px">
                  <input
                    class="input-box"
                    v-model="formNameData.crewRole1"
                    placeholder="请选择"
                    disabled
                    @click="openPicker('机长类型', 'pilotType1', 'crewRole1')"
                  />
                </FormItem>
              </van-col>
              <van-col span="10">
                <FormItem label-width="0px">
                  <input
                    class="input-box"
                    v-model="formNameData.captainId"
                    placeholder="请选择"
                    disabled
                    @click="openPicker('机长', 'pilot', 'captainId')"
                  />
                </FormItem>
              </van-col>
            </van-row>
            <van-row>
              <van-col span="14">
                <FormItem label="副驾驶：" label-width="70px">
                  <input
                    v-model="formNameData.crewRole2"
                    placeholder="请选择"
                    disabled
                    @click="openPicker('副驾驶类型', 'pilotType2', 'crewRole2')"
                  />
                </FormItem>
              </van-col>
              <van-col span="10">
                <FormItem label-width="0px">
                  <input
                    v-model="formNameData.copilotId"
                    placeholder="请选择"
                    disabled
                    @click="openPicker('副机长', 'pilot', 'copilotId')"
                  />
                </FormItem>
              </van-col>
            </van-row>
            <FormItem label="备注：" label-width="70px">
              <input v-model="formIdData.flightRemark" placeholder="请输入" />
            </FormItem>
          </view>

          <view v-if="roleType.indexOf('2') > -1">
            <view class="section-title">机务部人员修改</view>
            <FormItem label="机械师：" label-width="70px">
              <input
                v-model="formNameData.mechanicMasterId"
                placeholder="请选择"
                disabled
                @click="openPicker('机械师', 'maintenance', 'mechanicMasterId')"
              />
            </FormItem>
            <FormItem label="机械员：" label-width="70px">
              <input
                v-model="formNameData.mechanicId"
                placeholder="请选择"
                disabled
                @click="openPicker('机械员', 'maintenance', 'mechanicId')"
              />
            </FormItem>
            <FormItem label="保障员：" label-width="70px">
              <input
                v-model="formNameData.safetyOfficerId"
                placeholder="请选择"
                disabled
                @click="openPicker('保障员', 'maintenance', 'safetyOfficerId')"
              />
            </FormItem>
            <FormItem label="备注：" label-width="70px">
              <input
                v-model="formIdData.maintenanceRemark"
                placeholder="请输入"
              />
            </FormItem>
          </view>

          <view v-if="roleType.indexOf('3') > -1">
            <view class="section-title">运控中心人员修改</view>
            <FormItem label="安检：" label-width="70px">
              <input
                v-model="formNameData.inspectorId"
                placeholder="请选择"
                disabled
                @click="openPicker('安检', 'operationControl', 'inspectorId')"
              />
            </FormItem>
            <FormItem label="现场组织：" label-width="70px">
              <input
                v-model="formNameData.organizationId"
                placeholder="请选择"
                disabled
                @click="
                  openPicker('现场组织', 'operationControl', 'organizationId')
                "
              />
            </FormItem>
            <FormItem label="责任运控：" label-width="70px">
              <input
                v-model="formNameData.ocUserId"
                placeholder="请选择"
                disabled
                @click="openPicker('责任运控', 'operationControl', 'ocUserId')"
              />
            </FormItem>
            <FormItem label="运控助理：" label-width="70px">
              <input
                v-model="formNameData.ocAssistantId"
                placeholder="请选择"
                disabled
                @click="
                  openPicker('运控助理', 'operationControl', 'ocAssistantId')
                "
              />
            </FormItem>
            <FormItem label="值班经理：" label-width="70px">
              <input
                v-model="formNameData.ocAssistantId"
                placeholder="请选择"
                disabled
                @click="
                  openPicker('值班经理', 'operationControl', 'dutyManagerId')
                "
              />
            </FormItem>
            <FormItem label="备注：" label-width="70px">
              <input v-model="formIdData.ocRemark" placeholder="请输入" />
            </FormItem>
          </view>

          <view v-if="roleType.indexOf('4') > -1">
            <view class="section-title">市场部人员修改</view>
            <FormItem label="售票：" label-width="70px">
              <input
                v-model="formNameData.conductorId"
                placeholder="请选择"
                disabled
                @click="openPicker('售票', 'conductor', 'conductorId')"
              />
            </FormItem>
            <FormItem label="备注：" label-width="70px">
              <input v-model="formIdData.marketRemark" placeholder="请输入" />
            </FormItem>
          </view>
        </view>
      </view>
      <!-- 提交按钮 -->
      <view class="submit-btn-box" @click="submitForm"> 确认接收</view>
    </view>
    <van-popup
      :show="pickerData.show"
      position="bottom"
      :lock-scroll="true"
      custom-style="max-height: 60%;"
    >
      <view class="popup-header">
        <text class="cancel" @click="closePicker">取消</text>
        <text class="title">{{ pickerData.title }}</text>
        <text class="confirm" @click="confirmPicker">确认</text>
      </view>
      <view class="popup-content">
        <van-radio-group @change="onChange" :value="formCheckedIdData" :max="1">
          <van-cell
            v-for="(item, index) in pickerData.list"
            :key="index"
            :title="item.userName"
            clickable
            @click="onRowChange(item.userId)"
          >
            <van-radio
              slot="right-icon"
              :name="item.userId"
              :value="item.userId"
            />
          </van-cell>
        </van-radio-group>
        <van-checkbox-group value="{{ result }}" bind:change="onChange">
          <van-checkbox name="a">复选框 a</van-checkbox>
          <van-checkbox name="b">复选框 b</van-checkbox>
          <van-checkbox name="c">复选框 c</van-checkbox>
        </van-checkbox-group>
      </view>
    </van-popup>
    <Background />
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import FormItem from './compoents/FormItem.vue'
import {
  listUserByRole,
  planConfirmFlightTask2,
  queryFlightTaskConfigDetail,
} from '../../api/flightTask'
import { SUCCESS_CODE } from '../../utils/constant'
import { getRelativeDateText } from '../../utils'
import Background from '../../components/Background/index.vue'

export default {
  name: 'sureDetail',
  components: { Background, CustomerNav, FormItem },
  data() {
    return {
      taskId: '',
      formData: {},
      formCheckedIdData: [], //选择id的数据
      //下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
      },
      formNameData: {}, //表单名称数据展示用
      formIdData: {}, //表单id数据
      staffObj: {},
      roleType: '',
    }
  },
  onLoad: function (option) {
    //todo
    // this.taskId = option.id
    this.taskId = 57
  },
  mounted() {
    this.getPickerListData().then(() => {
      this.getData()
    })
  },
  methods: {
    getRelativeDateText,

    //获取详情
    async getData() {
      const res = await queryFlightTaskConfigDetail({
        flightTaskConfigId: Number(this.taskId),
      })
      if (res.response.code === 200) {
        const data = res.response.data
        this.formData = data || {}
        const crew = data.crew
        if (crew) {
          this.formNameData = {
            ...crew,
            captainId: this.transformPickerData(crew.captainId, 'pilot'),
            conductorId: this.transformPickerData(
              data.conductorId,
              'conductor'
            ),
            copilotId: this.transformPickerData(crew.copilotId, 'pilot'),
            inspectorId: this.transformPickerData(
              crew.inspectorId,
              'operationControl'
            ),
            mechanicId: this.transformPickerData(
              crew.mechanicId,
              'maintenance'
            ),
            mechanicMasterId: this.transformPickerData(
              crew.mechanicMasterId,
              'maintenance'
            ),

            ocAssistantId: this.transformPickerData(
              crew.ocAssistantId,
              'operationControl'
            ),
            ocUserId: this.transformPickerData(
              crew.ocUserId,
              'operationControl'
            ),
            organizationId: this.transformPickerData(
              crew.organizationId,
              'operationControl'
            ),
            dutyManagerId: this.transformPickerData(
              crew.organizationId,
              'dutyManagerId'
            ),
            safetyOfficerId: this.transformPickerData(
              crew.safetyOfficerId,
              'maintenance'
            ),
          }
          this.formIdData = crew || {}
        }
      }
    },
    //获取下拉人员数据
    async getPickerListData() {
      const { response: res } = await listUserByRole()
      if (res.code === SUCCESS_CODE) {
        this.roleType = res.data.deptType.join(',')
        this.staffObj = {
          ...res.data.userMap,
          pilotType1: [
            { userName: '机长', userId: '机长' },
            { userName: '实习机长', userId: '实习机长' },
            { userName: '教员', userId: '教员' },
          ],
          pilotType2: [
            { userName: '副驾驶', userId: '副驾驶' },
            { userName: '副驾驶A', userId: '副驾驶A' },
            { userName: '学员', userId: '学员' },
            { userName: '同乘', userId: '同乘' },
          ],
        }
      }
    },

    openPicker(title, listKey, formKey, isMore = false) {
      this.pickerData = {
        show: true,
        title: title,
        list: this.staffObj[listKey],
        formKey: formKey,
        isMore: isMore,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
      }
      this.formCheckedIdData = ''
    },
    //根据选中的id展示名字
    transformPickerData(id, key) {
      if (id && key) {
        const name = this.staffObj[key].find(
          (item) => item.userId == id
        ).userName
        return name || ''
      }
    },
    confirmPicker() {
      if (this.formCheckedIdData) {
        this.formIdData[this.pickerData.formKey] = this.formCheckedIdData
        this.formNameData[this.pickerData.formKey] =
          this.pickerData.list.find(
            (item) => item.userId == this.formCheckedIdData
          ).userName || ''
      }
      this.closePicker()
    },
    onChange(event) {
      this.formCheckedIdData = event.detail
    },
    onRowChange(data) {
      this.formCheckedIdData = data
    },

    // 提交表单
    async submitForm() {
      const params = {
        ...this.formIdData,
        flightTaskConfigId: Number(this.taskId),
      }
      for (let key in params) {
        if (
          params[key] === undefined ||
          params[key] === '' ||
          params[key] === null
        ) {
          delete params[key]
        }
      }
      const { response } = await planConfirmFlightTask2(params)
      if (response.code === SUCCESS_CODE) {
        uni.showToast({
          title: response.msg || '操作成功',
          icon: 'success',
        })
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/flightTask/sure?status=2',
          })
        }, 1500)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../assets/css/common.less';

.receive-detail-page {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

.content {
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 表单样式 */
.form-box {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  /deep/ .text-row {
    word-break: break-all; /* 强制在单词内换行 */
    white-space: pre-line;

    .value-box {
      border-bottom: none;
      padding: 0;
    }

    .label-box {
      padding: 0;
      font-size: 14px;
      color: #323233;
    }

    .input-box {
      border-bottom: 1px solid #ccc;
    }
  }

  .time-row {
    white-space: nowrap;
  }

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }
}

.section-title {
  //font-weight: bold;
  font-size: 16px;
  padding: 12px 0 0 8px;
  position: relative;
  color: #2c5de5;

  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #2c5de5;
    //background: #333;
    position: absolute;
    //left: -6px;
    left: 0;
    //top: calc(50% + 2px);
    top: 50%;
    transform: translateY(50%);
  }
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  box-sizing: border-box;

  .cancel {
    color: #999;
    font-size: 12px;
  }

  .confirm {
    color: #2c5de5;
    font-size: 12px;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }
}

.divider {
  width: 100%;
  height: 1px;
  background: rgba(204, 204, 204, 0.5);
  margin: 12px 0;
}

.submit-btn-box {
  width: 60%;
  background: #2c5de5;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
  padding: 8px 16px;
  margin-top: 16px;
  text-align: center;
  margin-left: 50%;
  transform: translateX(-50%);

  &:active {
    opacity: 0.8;
  }
}

.plan-time-content {
  padding: 8px 1em;
  border: 1px dashed #ccc;
  border-radius: 4px;

  .plan-time-item {
    border-bottom: 1px dashed #ccc;

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
